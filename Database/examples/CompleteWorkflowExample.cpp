#include "DatabaseManager.h"
#include "TestRepository.h"
#include "Test.h"
#include <QCoreApplication>
#include <QDebug>
#include <QTimer>
#include <QFileInfo>

/**
 * @brief 完整的安全数据库工作流程示例
 * 
 * 演示完整的密码管理生命周期：
 * 1. 第一次运行：创建数据库，生成密码，加密存储
 * 2. 后续运行：检索密码，解密，连接数据库
 * 3. 密码更改：重新生成密码并更新存储
 */
class CompleteWorkflowExample : public QObject {
    Q_OBJECT

public:
    explicit CompleteWorkflowExample(QObject *parent = nullptr) 
        : QObject(parent), m_dbManager(nullptr), m_repository(nullptr) {
    }

    ~CompleteWorkflowExample() {
        delete m_repository;
        delete m_dbManager;
    }

    void runExample() {
        qDebug() << "=== 完整安全数据库工作流程示例 ===";
        
        QString dbPath = "workflow_example.db";
        
        // 检查是否存在加密密码配置
        QString encryptedPassword = DatabaseManager::retrieveEncryptedPassword(dbPath);
        bool hasStoredPassword = !encryptedPassword.isEmpty();
        
        if (hasStoredPassword) {
            qDebug() << "检测到存储的加密密码，将使用现有配置";
            connectWithStoredPassword(dbPath);
        } else {
            qDebug() << "未检测到存储的密码，将创建新的安全配置";
            createNewSecureSetup(dbPath);
        }
        
        // 10秒后演示密码更改
        QTimer::singleShot(10000, this, [this, dbPath]() {
            demonstratePasswordChange(dbPath);
        });
        
        // 15秒后退出
        QTimer::singleShot(15000, qApp, &QCoreApplication::quit);
    }

private slots:
    void createNewSecureSetup(const QString &dbPath) {
        qDebug() << "\n--- 创建新的安全配置 ---";
        
        m_dbManager = new DatabaseManager();
        
        // 自动生成密码并设置数据库
        bool result = m_dbManager->initialize(dbPath, true, 10,
            [this](const QString &generatedPassword) {
                qDebug() << "✓ 随机密码已生成（长度:" << generatedPassword.length() << "）";
                qDebug() << "✓ 密码已使用AES-256-CBC加密";
                qDebug() << "✓ 加密密钥已存储到系统密钥链";
                qDebug() << "✓ 加密密码已存储到配置文件";
                
                // 延迟确保数据库完全初始化
                QTimer::singleShot(1000, this, [this]() {
                    setupAndTestDatabase();
                });
            });
        
        if (result) {
            qDebug() << "新安全配置创建请求已提交";
        } else {
            qDebug() << "新安全配置创建失败";
        }
    }
    
    void connectWithStoredPassword(const QString &dbPath) {
        qDebug() << "\n--- 使用存储的密码连接 ---";
        
        m_dbManager = new DatabaseManager();
        
        // 使用存储的加密密码连接
        m_dbManager->initializeWithStoredPassword(dbPath, 10,
            [this](bool success, const QString &error) {
                if (success) {
                    qDebug() << "✓ 成功从系统密钥链检索加密密钥";
                    qDebug() << "✓ 成功从配置文件检索加密密码";
                    qDebug() << "✓ 密码解密成功";
                    qDebug() << "✓ 数据库连接成功";
                    
                    setupAndTestDatabase();
                } else {
                    qDebug() << "✗ 连接失败:" << error;
                }
            });
    }
    
    void setupAndTestDatabase() {
        qDebug() << "\n--- 设置数据库并测试操作 ---";
        
        if (!m_dbManager || !m_dbManager->isConnected()) {
            qDebug() << "数据库未连接";
            return;
        }
        
        // 创建表
        if (!m_dbManager->createTables()) {
            qDebug() << "创建表失败";
            return;
        }
        
        // 创建Repository
        m_repository = new TestRepository(m_dbManager);
        
        // 测试数据库操作
        Test testData;
        testData.set("name", "安全工作流程测试");
        testData.set("description", "完整的密码管理生命周期测试");
        testData.set("created_at", QDateTime::currentDateTime().toString(Qt::ISODate).toStdString());
        
        m_repository->createTest(testData, [](bool success, int id) {
            if (success) {
                qDebug() << "✓ 数据库操作测试成功，记录ID:" << id;
            } else {
                qDebug() << "✗ 数据库操作测试失败";
            }
        });
    }
    
    void demonstratePasswordChange(const QString &dbPath) {
        qDebug() << "\n--- 演示密码更改流程 ---";
        
        // 生成新密码
        QString newPassword = DatabaseManager::generateRandomPassword(24);
        qDebug() << "生成新密码（长度:" << newPassword.length() << "）";
        
        // 生成新的加密密钥
        QString newEncryptionKey = DatabaseManager::generateRandomPassword(32);
        
        // 加密新密码
        QString newEncryptedPassword = DatabaseManager::encryptPassword(newPassword, newEncryptionKey);
        
        // 更新存储
        QString baseName = QFileInfo(dbPath).baseName();
        QString keyStoreName = QString("db_key_%1").arg(baseName);
        
        // 存储新的加密密钥
        DatabaseManager::storeEncryptionKey(keyStoreName, newEncryptionKey,
            [dbPath, newEncryptedPassword](bool success, const QString &error) {
                if (success) {
                    // 存储新的加密密码
                    if (DatabaseManager::storeEncryptedPassword(dbPath, newEncryptedPassword)) {
                        qDebug() << "✓ 密码更改完成";
                        qDebug() << "✓ 新的加密密钥已存储到系统密钥链";
                        qDebug() << "✓ 新的加密密码已存储到配置文件";
                        
                        // 验证新密码可以正确检索和解密
                        verifyNewPassword(dbPath);
                    } else {
                        qDebug() << "✗ 存储新加密密码失败";
                    }
                } else {
                    qDebug() << "✗ 存储新加密密钥失败:" << error;
                }
            });
    }
    
    static void verifyNewPassword(const QString &dbPath) {
        qDebug() << "\n--- 验证新密码 ---";
        
        QString baseName = QFileInfo(dbPath).baseName();
        QString keyStoreName = QString("db_key_%1").arg(baseName);
        
        // 检索并验证新密码
        DatabaseManager::retrieveEncryptionKey(keyStoreName,
            [dbPath](bool success, const QString &key, const QString &error) {
                if (success) {
                    QString encryptedPassword = DatabaseManager::retrieveEncryptedPassword(dbPath);
                    if (!encryptedPassword.isEmpty()) {
                        QString decryptedPassword = DatabaseManager::decryptPassword(encryptedPassword, key);
                        if (!decryptedPassword.isEmpty()) {
                            qDebug() << "✓ 新密码验证成功";
                            qDebug() << "✓ 密码长度:" << decryptedPassword.length();
                        } else {
                            qDebug() << "✗ 新密码解密失败";
                        }
                    } else {
                        qDebug() << "✗ 检索加密密码失败";
                    }
                } else {
                    qDebug() << "✗ 检索加密密钥失败:" << error;
                }
            });
    }

private:
    DatabaseManager *m_dbManager;
    TestRepository *m_repository;
};

int main(int argc, char *argv[]) {
    QCoreApplication app(argc, argv);
    
    CompleteWorkflowExample example;
    example.runExample();
    
    return app.exec();
}

#include "CompleteWorkflowExample.moc"
